# MultiWarehouseAutoStockSyncTriggerTest 测试说明

## 概述

参考NormalAutoStockSyncTriggerTest重新生成了MultiWarehouseAutoStockSyncTriggerTest，这是一个完整的集成测试类，用于验证多仓库存同步触发器的功能。

## 测试类特点

### 1. 继承结构
```java
public class MultiWarehouseAutoStockSyncTriggerTest extends AbstractSpringTest
```
- 继承自AbstractSpringTest，支持Spring容器和数据库操作
- 使用真实的数据库环境进行集成测试

### 2. 测试数据管理
- **测试会员**: `api2017`
- **数据清理**: 每个测试前后都会清理测试数据，确保测试独立性
- **ID追踪**: 使用`testDataIds`列表追踪插入的测试数据ID，便于清理

## 主要测试方法

### 1. triggerSyncNormalFlowTest()
**测试目标**: 验证正常流程的多仓库存同步
**测试步骤**:
1. 创建包含平台仓库代码的测试数据
2. 插入数据库
3. 执行同步触发
4. 验证数据库状态变化
5. 确认平台仓库代码不为空（多仓特征）

### 2. triggerSyncWithNullRequestTest()
**测试目标**: 验证空请求的处理
**测试步骤**:
1. 传入null请求
2. 验证不会意外修改数据库
3. 检查异常处理

### 3. triggerSyncWithEmptyCommandsTest()
**测试目标**: 验证空命令列表的处理
**测试步骤**:
1. 传入空命令列表
2. 验证不会产生数据变化

### 4. testOneToManyWarehouseMappingTest()
**测试目标**: 验证一对多仓库代码映射逻辑
**测试步骤**:
1. 创建一个apiSysMatchId对应多个仓库代码的测试数据
2. 执行同步触发
3. 验证所有仓库都被正确处理
4. 确认仓库代码不重复

### 5. triggerSyncWithSingleCommandTest()
**测试目标**: 验证单个命令处理
**测试步骤**:
1. 创建单个命令的请求
2. 执行同步触发
3. 验证处理结果

### 6. triggerSyncWithSlowSpeedCommandTest()
**测试目标**: 验证慢速命令处理
**测试步骤**:
1. 创建慢速平台命令
2. 执行同步触发
3. 验证慢速处理逻辑

## 辅助方法

### 命令创建方法
- `createTestCommands()`: 创建多个平台的测试命令
- `createSingleTestCommand()`: 创建单个平台的测试命令
- `createSlowSpeedTestCommands()`: 创建慢速平台的测试命令

### 测试数据创建方法
- `createMultiWarehouseTestPlatData()`: 创建基础多仓测试数据
- `createOneToManyWarehouseTestData()`: 创建一对多仓库映射测试数据

### 数据库操作方法
- `insertTestPlatData()`: 插入测试数据到数据库
- `queryTestPlatData()`: 查询测试数据
- `cleanupTestData()`: 清理测试数据

## 关键测试点

### 1. 多仓数据特征验证
```java
// 验证多仓数据特征：平台仓库代码不为空
Assert.assertTrue("多仓数据应该有平台仓库代码", 
    StringUtils.isNotBlank(platDO.getPlatWarehouseCode()));
```

### 2. 一对多映射验证
```java
// 验证一对多映射：同一个apiSysMatchId对应多个仓库代码
List<ApiSysMatchPlatDO> sameMatchIdData = updatedData.stream()
    .filter(x -> x.getApiSysMatchId().equals(2001))
    .collect(Collectors.toList());

Assert.assertTrue("应该有多个仓库对应同一个apiSysMatchId", 
    sameMatchIdData.size() >= 2);
```

### 3. 仓库代码唯一性验证
```java
// 验证仓库代码不重复
Set<String> warehouseCodes = sameMatchIdData.stream()
    .map(ApiSysMatchPlatDO::getPlatWarehouseCode)
    .filter(StringUtils::isNotBlank)
    .collect(Collectors.toSet());

Assert.assertTrue("仓库代码应该不重复", warehouseCodes.size() >= 2);
```

## 运行说明

### 1. 环境要求
- Spring测试环境
- 数据库连接配置
- 测试会员`api2017`的数据库权限

### 2. 运行方式
```bash
# 运行单个测试方法
mvn test -Dtest=MultiWarehouseAutoStockSyncTriggerTest#triggerSyncNormalFlowTest

# 运行整个测试类
mvn test -Dtest=MultiWarehouseAutoStockSyncTriggerTest
```

### 3. 注意事项
- 测试会直接操作数据库，确保测试环境数据安全
- 每个测试都会自动清理数据，但建议在独立的测试环境运行
- 如果某些Mapper方法不存在（如selectByIds、deleteById），需要先实现这些方法

## 与NormalAutoStockSyncTriggerTest的区别

| 特性 | NormalAutoStockSyncTriggerTest | MultiWarehouseAutoStockSyncTriggerTest |
|------|-------------------------------|---------------------------------------|
| 测试目标 | 普通库存同步 | 多仓库存同步 |
| 数据特征 | plat_warehouse_code为空 | plat_warehouse_code不为空 |
| 核心验证 | 普通同步流程 | 一对多仓库映射 |
| 测试重点 | 基础同步功能 | 仓库代码处理逻辑 |

## 预期测试结果

所有测试方法都应该通过，验证：
1. 多仓库存同步触发器能正确处理包含平台仓库代码的数据
2. 一对多仓库映射逻辑工作正常
3. 异常情况处理得当
4. 数据库状态变化符合预期

package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.plugins.stock.notice;

import com.differ.wdgj.api.component.util.tools.NumberUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockNoticeConvertTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.notice.ApiStockNoticeSaveResult;
import com.differ.wdgj.api.user.biz.domain.stock.notice.StockNoticeFacade;
import com.differ.wdgj.api.user.biz.domain.stock.utils.SyncStockJobUtils;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AutoJobTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.user.AbstractUserSimpleDistributeJob;
import com.differ.wdgj.api.user.biz.tasks.job.schedule.JobExecTimeStrategy;
import com.differ.wdgj.api.user.biz.tasks.job.schedule.SingleJobParameter;
import com.differ.wdgj.api.user.biz.tasks.job.schedule.SimpleDistributeJobParameter;
import com.differ.wdgj.api.user.biz.tasks.job.schedule.strategy.AverageJobShardingStrategy;
import com.differ.wdgj.api.user.biz.tasks.job.schedule.strategy.EurekaContextStrategy;
import com.differ.wdgj.common.constant.SiteTypeCodeConst;
import org.slf4j.Logger;

import java.util.List;

/**
 * 多仓库存变动通知转换定时任务
 * <p>
 * 实现[系统货品维度] => [平台商品维度]的多仓库存同步
 *
 * <AUTHOR> Assistant
 * @date 2025-08-12
 */
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "MultiWareStockNoticeConvertJob"
)
@SimpleDistributeJobParameter(
        jobContextStrategy = EurekaContextStrategy.class,
        jobShardingStrategy = AverageJobShardingStrategy.class
)
public class MultiWareStockNoticeConvertJob extends AbstractUserSimpleDistributeJob {

    // region 变量

    /**
     * 标题
     */
    private static final String CAPTION = "多仓库存变动通知转换定时任务";

    /**
     * 日志
     */
    private final Logger log = LogFactory.get(CAPTION);

    /**
     * 库存变动通知门面
     */
    private final StockNoticeFacade stockNoticeFacade = new StockNoticeFacade();

    // endregion

    // region 实现基类方法

    /**
     * 日志标题
     *
     * @return 日志标题
     */
    @Override
    protected String logCaption() {
        return CAPTION;
    }

    /**
     * 获取待执行会员
     *
     * @return 会员集合
     */
    @Override
    protected List<String> getExecutedUsers() {
        return SyncStockJobUtils.getAutoMembers(AutoJobTypeEnum.AUTO_MULTI_WAREHOUSE_STOCK_SYNC);
    }

    /**
     * 实际的执行时间策略（执行频率）
     *
     * @return 执行时间策略
     */
    @Override
    protected JobExecTimeStrategy getExecTimeStrategy() {
        String runFrequencyStr = ConfigKeyUtils.getConfigValue(ConfigKeyEnum.STOCK_NOTICE_MULTI_CONVERT_FREQUENCY);
        this.execTimeStrategy.setRunFrequency(NumberUtils.toLong(runFrequencyStr, 30));
        return execTimeStrategy;
    }

    /**
     * 线程池
     *
     * @return 线程池
     */
    @Override
    protected TaskEnum taskPool() {
        return TaskEnum.API_MULTI_STOCK_NOTICE_CONVERT;
    }

    /**
     * 按会员执行任务
     *
     * @param memberName 会员名
     */
    @Override
    protected void executeByUser(String memberName) {
        try {
            log.info("开始执行多仓库存变动通知转换，会员：{}", memberName);

            // 调用库存变动通知转换
            StockContentResult<ApiStockNoticeSaveResult> result = stockNoticeFacade.convertErpToPlatStockNotice(
                    memberName, 
                    StockNoticeConvertTypeEnum.MULTI_WARE
            );

            // 处理结果
            if (result != null && result.getSuccess()) {
                if (result.getContent() != null) {
                    log.info("多仓库存变动通知转换成功，会员：{}，转换数量：{}", 
                            memberName, result.getContent().getApiStockNoticeCount());
                } else {
                    log.info("多仓库存变动通知转换成功，会员：{}，但获取转换数量失败", memberName);
                }
            } else {
                String errorMsg = result != null ? result.getMessage() : "返回结果为空";
                log.warn("多仓库存变动通知转换失败，会员：{}，失败原因：{}", memberName, errorMsg);
            }

        } catch (Exception e) {
            log.error("多仓库存变动通知转换异常，会员：{}", memberName, e);
        }
    }

    // endregion
}

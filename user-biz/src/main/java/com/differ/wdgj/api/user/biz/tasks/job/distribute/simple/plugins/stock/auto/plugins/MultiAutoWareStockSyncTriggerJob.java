package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.plugins.stock.auto.plugins;

import com.differ.wdgj.api.component.task.single.core.JobExecTimeStrategy;
import com.differ.wdgj.api.component.task.single.core.SingleJobParameter;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerJobTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.command.IAutoStockSyncCommandResolver;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.command.plugins.MultiWarehouseAutoStockSyncCommandResolver;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger.IAutoStockSyncTrigger;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger.impl.MultiWarehouseAutoStockSyncTrigger;
import com.differ.wdgj.api.user.biz.domain.stock.utils.SyncStockJobUtils;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AutoJobTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.plugins.stock.auto.AbstractAutoStockSyncTriggerJob;
import net.logstash.logback.encoder.org.apache.commons.lang.math.NumberUtils;

import java.util.List;

/**
 * 多仓自动库存同步触发定时任务
 * <p>
 * 需要传仓库/门店信息的库存同步
 * 适用于多仓平台的库存同步需求
 *
 * 业务逻辑已委托给MultiWarehouseAutoStockSyncTrigger处理
 *
 * <AUTHOR>
 * @date 2025/8/11 15:50
 */
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "MultiAutoWareStockSyncTriggerJob",
        cron = "0/3 * * * * ?"
)
public class MultiAutoWareStockSyncTriggerJob extends AbstractAutoStockSyncTriggerJob {
    /**
     * 获取待执行会员
     *
     * @return 会员集合
     */
    @Override
    protected List<String> getExecutedUsers() {
        return SyncStockJobUtils.getAutoMembers(AutoJobTypeEnum.AUTO_MULTI_WAREHOUSE_STOCK_SYNC);
    }

    /**
     * 实际的执行时间策略（执行频率）
     *
     * @return 执行时间策略
     */
    @Override
    protected JobExecTimeStrategy getExecTimeStrategy() {
        String runFrequencyStr = ConfigKeyUtils.getConfigValue(ConfigKeyEnum.STOCK_NOTICE_MULTI_TRIGGER_FREQUENCY);
        this.execTimeStrategy.setRunFrequency(NumberUtils.toLong(runFrequencyStr, 20));
        return execTimeStrategy;
    }

    /**
     * 获取自动任务类型
     *
     * @return 自动任务类型枚举
     */
    @Override
    protected StockSyncTriggerJobTypeEnum getAutoJobType() {
        return StockSyncTriggerJobTypeEnum.MULTI_WARE;
    }

    /**
     * 获取自动库存触发命令解析器
     *
     * @return 获取自动库存触发命令解析器
     */
    @Override
    protected IAutoStockSyncCommandResolver getCommandResolver() {
        return new MultiWarehouseAutoStockSyncCommandResolver();
    }

    /**
     * 获取自动库存同步触发器
     *
     * @return 自动库存同步触发器
     */
    @Override
    protected IAutoStockSyncTrigger getAutoStockSyncTrigger() {
        return new MultiWarehouseAutoStockSyncTrigger();
    }

    /**
     * 线程池
     *
     * @return 线程池
     */
    @Override
    protected TaskEnum taskPool() {
        return TaskEnum.API_MULTI_WAREHOUSE_STOCK_SYNC;
    }
}

package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.plugins.stock;

import com.differ.wdgj.api.component.task.single.core.SingleJobParameter;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchPlatMapper;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.user.AbstractUserSimpleDistributeJob;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 自动删除g_api_sysmatch_plat表历史数据 定时任务
 *
 * <AUTHOR>
 * @date 2025/8/11 15:30
 */
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "DeleteApiSysMatchPlatHistoryJob",
        cron = "0 0 2 * * ?" //每天凌晨2点执行一次
)
public class DeleteApiSysMatchPlatHistoryJob extends AbstractUserSimpleDistributeJob {
    //region 变量

    /**
     * 标题
     */
    private final String CAPTION = "自动删除g_api_sysmatch_plat表历史数据";

    /**
     * 平台商品库存变动记录 Mapper
     */
    @Autowired
    private ApiSysMatchPlatMapper apiSysMatchPlatMapper;

    // endregion

    //region 实现基类方法

    /**
     * 日志标题
     *
     * @return 日志标题
     */
    @Override
    protected String logCaption() {
        return CAPTION;
    }

    /**
     * 按会员执行任务
     *
     * @param memberName 会员名
     */
    @Override
    protected void executeByUser(String memberName) {
        //是否开启 总开关
        if (!ConfigKeyUtils.isActionApiBoolean(ConfigKeyEnum.ISACTION_DELETE_SYSMATCH_PLAT_EXPIRED_DATA)) {
            return;
        }

        try {
            // 获取配置的过期天数，默认15天
            String expiredDayStr = ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.SYSMATCH_PLAT_EXPIRED_DAY);
            int expiredDays = NumberUtils.toInt(expiredDayStr, 15);

            LogFactory.info(CAPTION, String.format("[%s]开始删除g_api_sysmatch_plat表%d天前的历史数据", memberName, expiredDays));

            // 分页删除，避免一次删除过多数据影响性能
            int totalDeletedCount = 0;
            int batchDeletedCount;
            final int batchSize = 1000; // 每批次删除的数量

            do {
                batchDeletedCount = DBSwitchUtil.doDBWithUser(memberName, () ->
                    apiSysMatchPlatMapper.deleteExpiredData(expiredDays, batchSize));
                totalDeletedCount += batchDeletedCount;

                // 如果删除了数据，记录日志并稍作休息
                if (batchDeletedCount > 0) {
                    LogFactory.info(CAPTION, String.format("[%s]本批次删除%d条历史数据", memberName, batchDeletedCount));

                    // 避免对数据库造成过大压力，稍作休息
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            } while (batchDeletedCount >= batchSize); // 当删除数量小于batchSize时停止（说明已经删除完毕）

            if (totalDeletedCount > 0) {
                LogFactory.info(CAPTION, String.format("[%s]删除g_api_sysmatch_plat表历史数据完成，共删除%d条记录", memberName, totalDeletedCount));
            } else {
                LogFactory.info(CAPTION, String.format("[%s]无需删除g_api_sysmatch_plat表历史数据", memberName));
            }

        } catch (Throwable e) {
            LogFactory.error(CAPTION, String.format("[%s]删除g_api_sysmatch_plat表历史数据异常", memberName), e);
        }
    }

    //endregion
}

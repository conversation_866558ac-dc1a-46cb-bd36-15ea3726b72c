package com.differ.wdgj.api.user.biz.infrastructure.thread.pool;

import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.*;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.reject.CallerRunsRejectedPolicy;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.reject.WaitSecondAndRejectedPolicy;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;
import org.springframework.util.concurrent.ListenableFutureTask;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.IntSupplier;

/**
 * @Description 线程池任务类型
 * 线程池使用选择说明：
 * 1.对于普通的场景，量不大的可直接使用API_COMMON
 * 2.如果遇到特殊业务需要独立控制线程池参数的，请单独添加枚举，比如四大功能的自动抓单任务
 * <AUTHOR>
 * @Date 2021/11/19 17:36
 */
public enum TaskEnum {
    /**
     * api通用线程池，四大功能之外的处理量比较小的
     */
    API_SYNC(1, 1, 0L, Integer.MAX_VALUE, 2000, CallerRunsRejectedPolicy.class, "api-sync", "API同步"),

    /**
     * api通用线程池，四大功能之外的处理量比较小的
     */
    API_COMMON(1.0, 1.0, 0L, Integer.MAX_VALUE, 2000, CallerRunsRejectedPolicy.class, "api-common", "API通用"),

    /**
     * 执行监控队列
     */
    API_EXEC_MONITOR_QUEUE(1, 1, 60L, TimeUnit.SECONDS, 1000, 500, ThreadPoolExecutor.AbortPolicy.class, "api-exec-monitor-queue", "执行监控队列"),

    /**
     * quartz执行线程
     */
    API_QUARTZ_EXEC(10, 10, 10L, TimeUnit.SECONDS, 200, 5, WaitSecondAndRejectedPolicy.class, "api-quartz-exec", "quartz执行线程"),

    /**
     * 多队列数据库消费者
     */
    API_MULTI_DB_CONSUMER(2, 2, 10L, TimeUnit.SECONDS, 5000, Integer.MAX_VALUE, ThreadPoolExecutor.AbortPolicy.class, "api_multi_db_consumer", "多队列全局数据库消费者"),

    /**
     * 多队列kafka消费者
     */
    API_MULTI_KAFKA_CONSUMER(1, 1, 10L, TimeUnit.SECONDS, 5000, Integer.MAX_VALUE, ThreadPoolExecutor.AbortPolicy.class, "api_multi_kafka_consumer", "多队列kafka消费者"),

    /**
     * 触发达到自动下载售后单时间间隔的普通店铺
     */
    API_AFTER_SALE_LOAD_QUEUE(10, 20, 60L, TimeUnit.SECONDS, 5000, Integer.MAX_VALUE, CallerRunsRejectedPolicy.class, "api_after_sale_auto_load", "自动下载售后单"),

    /**
     * api自动下载售后单线程池
     */
    API_AFTER_SALE_LOAD(10, 20, 60L, TimeUnit.SECONDS, 10000, Integer.MAX_VALUE, CallerRunsRejectedPolicy.class, "api_after_sale_load", "下载售后单"),

    /**
     * api自动下载淘宝售后单线程池
     */
    API_TAOBAO_AFTER_SALE_LOAD(10, 20, 60L, TimeUnit.SECONDS, 10000, Integer.MAX_VALUE, CallerRunsRejectedPolicy.class, "api_taobao_after_sale_load", "下载淘宝售后单"),

    /**
     * api下载售后单通知线程池
     */
    API_AFTER_SALE_NOTIFY(2, 5, 60L, TimeUnit.SECONDS, 2000, Integer.MAX_VALUE, CallerRunsRejectedPolicy.class, "api_after_sale_notify", "下载售后单触发通知"),
    /**
     * api异步报警线程池
     */
    API_ALARM(1, 1, 10L, TimeUnit.SECONDS, 1000, Integer.MAX_VALUE, ThreadPoolExecutor.AbortPolicy.class, "api-alarm", "监控报警"),
    /**
     * API 手动库存同步菠萝派请求
     */
    API_STOCK_POLY_REQUEST_MANUAL(2.0, 5.0, 60L, 2000, 500, CallerRunsRejectedPolicy.class, "api-stock-poly-request-manual", "API 手动库存同步菠萝派请求"),
    /**
     * API 自动库存同步菠萝派请求
     */
    API_STOCK_POLY_REQUEST_AUTO(2.0, 5.0, 60L, 5000, 2000, CallerRunsRejectedPolicy.class, "api-stock-poly-request-auto", "API 自动库存同步菠萝派请求"),
    /**
     * API 自动库存同步菠萝派请求
     */
    API_STOCK_POLY_REQUEST_SLOW_PLAT(2.0, 5.0, 60L, 5000, 2000, CallerRunsRejectedPolicy.class, "api-stock-poly-request-auto", "API 自动库存同步菠萝派请求"),
    /**
     * API 通用库存同步
     */
    API_NORMAL_STOCK_SYNC(2.0, 5.0, 60L, 2000, 500, CallerRunsRejectedPolicy.class, "api-normal-stock-sync", "API 通用库存同步"),
    /**
     * API 慢平台库存同步
     */
    API_SLOW_PLAT_STOCK_SYNC(2.0, 5.0, 60L, 2000, 500, CallerRunsRejectedPolicy.class, "api-slow-plat-stock-sync", "API 慢平台库存同步"),
    /**
     * API 多仓库存同步
     */
    API_MULTI_WAREHOUSE_STOCK_SYNC(2.0, 5.0, 60L, 2000, 500, CallerRunsRejectedPolicy.class, "api-multi-warehouse-stock-sync", "API 多仓库存同步"),
    /**
     * API 普通库存变动通知转换
     */
    API_NORMAL_STOCK_NOTICE_CONVERT(2.0, 5.0, 60L, 2000, 500, CallerRunsRejectedPolicy.class, "api-normal-stock-notice-convert", "API 普通库存变动通知转换"),
    /**
     * API 多仓库存变动通知转换
     */
    API_MULTI_STOCK_NOTICE_CONVERT(2.0, 5.0, 60L, 2000, 500, CallerRunsRejectedPolicy.class, "api-multi-stock-notice-convert", "API 多仓库存变动通知转换"),
    /**
     * api补偿下载售后单线程池
     */
    API_SUPPLE_AFTER_SALE_LOAD(10, 20, 60L, TimeUnit.SECONDS, 10000, Integer.MAX_VALUE, CallerRunsRejectedPolicy.class, "api_supple_after_sale_load", "补偿下载售后单"),
    ;

    /**
     * 核心线程数的CPU核数的倍数
     */
    private double corePoolSizeCpuMultiple;
    /**
     * 最大线程数
     */
    private double maximumPoolSizeCpuMultiple;
    /**
     * 线程空闲后存活时间
     */
    private long keepAliveTime;
    /**
     * keepAliveTime的单位
     */
    private TimeUnit unit;
    /**
     * 任务阻塞队列长度上限
     */
    private int queueCapacity;
    /**
     * 触发阻塞队列报警的上限
     */
    private int blockingQueueAlarmLimit;
    /**
     * 线程名称前缀
     */
    private String namePreFix;
    /**
     * 线程池业务中文描述
     */
    private String description;
    /**
     * 拒绝策略
     */
    private Class<? extends RejectedExecutionHandler> rejectedHandlerClass;

    /**
     * 触发阻塞队列报警的上限的设置函数
     */
    private IntSupplier funBlockingQueueAlarmLimit;

    /**
     * 拒绝策略bean名
     */
    private String rejectedHandlerBean;
    /**
     * 核心线程数（有值时优先）
     */
    private int corePoolSize;
    /**
     * 最大线程数（有值时优先）
     */
    private int maximumPoolSize;

    /**
     * 一般推荐使用这个构造
     *
     * @param corePoolSizeCpuMultiple
     * @param maximumPoolSizeCpuMultiple
     * @param keepAliveTime
     * @param queueCapacity
     * @param blockingQueueAlarmLimit
     * @param rejectedHandlerClass
     * @param namePreFix
     * @param description
     */
    @Deprecated
    TaskEnum(double corePoolSizeCpuMultiple, double maximumPoolSizeCpuMultiple, long keepAliveTime, int queueCapacity, int blockingQueueAlarmLimit, Class<? extends RejectedExecutionHandler> rejectedHandlerClass, String namePreFix, String description) {
        this(corePoolSizeCpuMultiple, maximumPoolSizeCpuMultiple, keepAliveTime, TimeUnit.SECONDS, queueCapacity, blockingQueueAlarmLimit, rejectedHandlerClass, namePreFix, description);
    }

    /**
     * 一般不推荐使用，特殊的必须指定核心线程数的采用
     *
     * @param corePoolSize
     * @param maximumPoolSize
     * @param keepAliveTime
     * @param unit
     * @param queueCapacity
     * @param blockingQueueAlarmLimit
     * @param rejectedHandlerClass
     * @param namePreFix
     * @param description
     */
    TaskEnum(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, int queueCapacity, int blockingQueueAlarmLimit, Class<? extends RejectedExecutionHandler> rejectedHandlerClass, String namePreFix, String description) {
        this.corePoolSize = corePoolSize;
        this.maximumPoolSize = maximumPoolSize;
        this.keepAliveTime = keepAliveTime;
        this.unit = unit;
        this.queueCapacity = queueCapacity;
        this.blockingQueueAlarmLimit = blockingQueueAlarmLimit;
        this.namePreFix = namePreFix;
        this.description = description;
        this.rejectedHandlerClass = rejectedHandlerClass;
    }

    TaskEnum(double corePoolSizeCpuMultiple, double maximumPoolSizeCpuMultiple, long keepAliveTime, TimeUnit unit, int queueCapacity, int blockingQueueAlarmLimit, Class<? extends RejectedExecutionHandler> rejectedHandlerClass, String namePreFix, String description) {
        this.corePoolSizeCpuMultiple = corePoolSizeCpuMultiple;
        this.maximumPoolSizeCpuMultiple = maximumPoolSizeCpuMultiple;
        this.keepAliveTime = keepAliveTime;
        this.unit = unit;
        this.queueCapacity = queueCapacity;
        this.blockingQueueAlarmLimit = blockingQueueAlarmLimit;
        this.namePreFix = namePreFix;
        this.description = description;
        this.rejectedHandlerClass = rejectedHandlerClass;
    }

    TaskEnum(double corePoolSizeCpuMultiple, double maximumPoolSizeCpuMultiple, long keepAliveTime, TimeUnit unit, int queueCapacity, int blockingQueueAlarmLimit, String rejectedHandlerBean, String namePreFix, String description) {
        this.corePoolSizeCpuMultiple = corePoolSizeCpuMultiple;
        this.maximumPoolSizeCpuMultiple = maximumPoolSizeCpuMultiple;
        this.keepAliveTime = keepAliveTime;
        this.unit = unit;
        this.queueCapacity = queueCapacity;
        this.blockingQueueAlarmLimit = blockingQueueAlarmLimit;
        this.namePreFix = namePreFix;
        this.description = description;
        this.rejectedHandlerBean = rejectedHandlerBean;
    }

    public long getKeepAliveTime() {
        return keepAliveTime;
    }

    public TimeUnit getUnit() {
        return unit;
    }

    public int getQueueCapacity() {
        return queueCapacity;
    }

    public int getBlockingQueueAlarmLimit() {
        if (funBlockingQueueAlarmLimit != null) {
            return funBlockingQueueAlarmLimit.getAsInt();
        }
        int alarmValue = TaskCustomParamUtil.getCustomBlockingQueueAlarmValue(this.namePreFix);
        if (alarmValue > 0) {
            return alarmValue;
        }
        return blockingQueueAlarmLimit;
    }

    public String getNamePreFix() {
        return namePreFix;
    }

    public String getDescription() {
        return description;
    }

    public Class<? extends RejectedExecutionHandler> getRejectedHandlerClass() {
        return rejectedHandlerClass;
    }

    public String getRejectedHandlerBean() {
        return rejectedHandlerBean;
    }

    public int getCorePoolSize() {
        // 优先取自定义配置
        int corePoolSizeAsInt = TaskCustomParamUtil.getCustomCorePoolSize(this.namePreFix);
        if (corePoolSizeAsInt > 0) {
            return corePoolSizeAsInt;
        }
        if (this.corePoolSize > 0) {
            return this.corePoolSize;
        }
        return new Double(this.corePoolSizeCpuMultiple * TaskCustomParamUtil.getCpuCount()).intValue();
    }

    public int getMaximumPoolSize() {
        // 优先取自定义配置
        int maximumPoolSizeAsInt = TaskCustomParamUtil.getCustomMaximumPoolSize(this.namePreFix);
        if (maximumPoolSizeAsInt > 0) {
            return maximumPoolSizeAsInt;
        }
        if (this.maximumPoolSize > 0) {
            return this.maximumPoolSize;
        }
        return new Double(this.maximumPoolSizeCpuMultiple * TaskCustomParamUtil.getCpuCount()).intValue();
    }

    public void setFunBlockingQueueAlarmLimit(IntSupplier funBlockingQueueAlarmLimit) {
        this.funBlockingQueueAlarmLimit = funBlockingQueueAlarmLimit;
    }

    /**
     * 执行带吉客号的任务
     *
     * @param command
     */
    public void executeJack(MemberRunnable command) {
        execute(command);
    }

    /**
     * 执行带返回值的任务
     *
     * @param task
     * @param <T>
     * @return
     */
    public <T> Future<T> submitJack(MemberCallable<T> task) {
        return this.submitListenable(task);
    }

    /**
     * 可监听执行结果
     *
     * @param task
     * @return
     */
    public ListenableFuture<Object> submitListenable(Runnable task) {
        ListenableFutureTask<Object> future = ListenableFutureCustomTaskFactory.create(task);
        this.getPoolExecutor().execute(future);
        return future;
    }

    /**
     * 可监听执行结果，带返回值
     *
     * @param task
     * @param <T>
     * @return
     */
    public <T> ListenableFuture<T> submitListenable(Callable<T> task) {
        ListenableFutureTask<T> future = ListenableFutureCustomTaskFactory.create(task);
        this.getPoolExecutor().execute(future);
        return future;
    }

    /**
     * 带回调结果的异步执行
     *
     * @param task
     * @param callbackComplete
     * @return
     */
    public <R> ListenableFuture<R> submitListenable(Callable<R> task, ListenableFutureCallback<? super R> callbackComplete) {
        ListenableFutureTask<R> future = ListenableFutureCustomTaskFactory.create(task);
        future.addCallback(callbackComplete);
        this.getPoolExecutor().execute(future);
        return future;
    }

    /**
     * 带回调结果的异步执行
     *
     * @param task
     * @param callbackComplete
     * @return
     */
    public <R> ListenableFuture<R> submitListenable(Callable<R> task, BiConsumer<R, ? super Throwable> callbackComplete) {
        ListenableFutureTask<R> future = ListenableFutureCustomTaskFactory.create(task);
        future.addCallback(result -> callbackComplete.accept(result, null), t -> callbackComplete.accept(null, t));
        this.getPoolExecutor().execute(future);
        return future;
    }

    /**
     * 带回调结果的异步执行
     *
     * @param task
     * @param callbackComplete
     * @return
     */
    public void execute(Runnable task, Consumer<? super Throwable> callbackComplete) {
        ListenableFutureTask<Object> future = ListenableFutureCustomTaskFactory.create(task);
        future.addCallback(result -> callbackComplete.accept(null), t -> callbackComplete.accept(t));
        this.getPoolExecutor().execute(future);
    }

    /**
     * 执行不带返回值的任务
     *
     * @param command
     */
    public void execute(Runnable command) {
        this.getPoolExecutor().execute(command);
    }

    /**
     * 执行一批任务，并且等待所有任务执行完毕
     *
     * @param concurrentExecLimit 任务并发执行上限
     * @param tasks
     */
    public void execAndWaitAll(int concurrentExecLimit, Runnable... tasks) {
        SteadyRunnableExecutor executor = new SteadyRunnableExecutor(concurrentExecLimit, this);
        for (Runnable task : tasks) {
            executor.exec(task);
        }
        executor.waitAll();
    }

    /**
     * 执行一批任务，并且等待所有任务执行完毕
     *
     * @param timeoutSecond 超时秒
     * @param tasks
     */
    public void execAndWaitAll(long timeoutSecond, Runnable... tasks) {
        List<Future<?>> results = new ArrayList<>();
        for (Runnable task : tasks) {
            Future<?> submit = this.submitListenable(task);
            results.add(submit);
        }

        // 等待所有任务的结果
        results.forEach(r -> {
            try {
                if (timeoutSecond > 0) {
                    r.get(timeoutSecond, TimeUnit.SECONDS);
                } else {
                    r.get();
                }
            } catch (InterruptedException interruptedException) {
                LogFactory.get(TaskEnum.class).error("线程任务执行中断", interruptedException);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                LogFactory.get(TaskEnum.class).error("线程任务执行异常", e);
            } finally {
                if (!r.isDone()) {
                    // 异常或其他特殊未完成的取消掉
                    r.cancel(true);
                }
                r = null;
            }
        });
    }

    /**
     * 执行一批任务，并且等待所有任务执行完毕
     *
     * @param tasks
     */
    public void execAndWaitAll(Runnable... tasks) {
        List<Future<?>> results = new ArrayList<>();
        for (Runnable task : tasks) {
            Future<?> submit = this.submitListenable(task);
            results.add(submit);
        }

        // 等待所有任务的结果
        results.forEach(r -> {
            try {
                r.get();
            } catch (InterruptedException interruptedException) {
                LogFactory.get(TaskEnum.class).error("线程任务执行中断", interruptedException);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                LogFactory.get(TaskEnum.class).error("线程任务执行异常", e);
            } finally {
                if (!r.isDone()) {
                    // 异常或其他特殊未完成的取消掉
                    r.cancel(true);
                }
                r = null;
            }
        });
    }

    /**
     * 执行一批任务，并且等待所有任务执行完毕
     *
     * @param tasks
     */
    public void execAndWaitAll(List<Runnable> tasks) {
        this.execAndWaitAll(tasks.toArray(new MemberRunnable[0]));
    }

    /**
     * 执行一批任务，并且等待所有任务执行完毕
     *
     * @param tasks
     * @param <T>
     * @return
     */
    public <T> List<T> execAndWaitAll(Callable<T>... tasks) {
        List<Future<T>> resultFutures = new ArrayList<>();
        for (Callable<T> task : tasks) {
            Future<T> submit = this.submitListenable(task);
            resultFutures.add(submit);
        }

        // 等待所有任务的结果
        List<T> results = new ArrayList<>();
        resultFutures.forEach(r -> {
            try {
                results.add(r.get());
            } catch (InterruptedException interruptedException) {
                LogFactory.get(TaskEnum.class).error("线程任务执行中断", interruptedException);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                LogFactory.get(TaskEnum.class).error("线程任务执行异常", e);
            } finally {
                if (!r.isDone()) {
                    // 异常或其他特殊未完成的取消掉
                    r.cancel(true);
                }
                r = null;
            }
        });
        return results;
    }

    /**
     * 执行一批任务，当达到线程报警值时，同步执行
     *
     * @param tasks 执行任务
     */
    public void execOnAlarmWaitAll(Runnable... tasks) {
        this.execOnAlarmWaitAll(0L, tasks);
    }

    /**
     * 执行一批任务，当达到线程报警值时，同步执行
     *
     * @param tasks timeoutSecond 同步执行的最大等待超时时间
     * @param tasks 执行任务
     */
    public void execOnAlarmWaitAll(long timeoutSecond, Runnable... tasks) {
        // 判断线程数是否大于报警值，是则同步执行
        if (this.getPoolExecutor().getExecuteAndWait().getTotalCount() > this.getBlockingQueueAlarmLimit()) {
            this.execAndWaitAll(timeoutSecond, tasks);
            return;
        }

        // 正常执行
        for (Runnable task : tasks) {
            this.getPoolExecutor().execute(task);
        }
    }

    /**
     * 等待全部任务执行完成
     *
     * @param futures 任务凭证
     */
    public static void waitAllCompleted(List<Future<?>> futures) {

        // 等待所有任务的结果
        futures.forEach(future -> {
            try {
                future.get();
            } catch (InterruptedException interruptedException) {
                LogFactory.get(TaskEnum.class).error("线程任务执行中断", interruptedException);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                LogFactory.get(TaskEnum.class).error("线程任务执行异常", e);
            } finally {

                // 异常或其他特殊未完成的取消掉
                if (!future.isDone()) {
                    future.cancel(true);
                }

                future = null;
            }
        });
    }

    /**
     * 等待全部任务执行完成
     *
     * @param futures 任务凭证
     */
    public static <T> List<T> waitAllCompletedResult(List<Future<T>> futures) {

        // 等待所有任务的结果
        List<T> results = new ArrayList<>();
        futures.forEach(future -> {
            try {
                results.add(future.get());
            } catch (InterruptedException interruptedException) {
                LogFactory.get(TaskEnum.class).error("线程任务执行中断", interruptedException);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                LogFactory.get(TaskEnum.class).error("线程任务执行异常", e);
            } finally {

                // 异常或其他特殊未完成的取消掉
                if (!future.isDone()) {
                    future.cancel(true);
                }

                future = null;
            }
        });

        return results;
    }

    /**
     * 带回调的异步执行(不推荐，因为他不支持jack号的识别与绑定)
     * 推荐使用 {@link TaskEnum#execute(Runnable, Consumer)}
     *
     * @param runnable 执行方法
     * @param callback 执行完成回调
     * @return 凭证
     */
    @Deprecated
    public void runAsyncWithCallback(Runnable runnable, BiConsumer<? super Void, ? super Throwable> callback) {
        CompletableFuture.runAsync(runnable, this.getPoolExecutor()).whenComplete(callback);
    }

    /**
     * 带回调结果的异步执行(不推荐，因为他不支持jack号的识别与绑定)
     * 推荐使用 {@link TaskEnum#submitListenable(Callable, BiConsumer)}
     *
     * @param callable
     * @param callbackComplete
     * @return
     */
    @Deprecated
    public <R> CompletableFuture<R> supplyAsyncWithCallback(Callable<R> callable, BiConsumer<R, ? super Throwable> callbackComplete) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return callable.call();
            } catch (Exception e) {
                throw new RuntimeException("异步执行异常", e);
            }
        }, this.getPoolExecutor()).whenComplete(callbackComplete);
    }

    /**
     * 获取线程池
     *
     * @return
     */
    public BaseThreadPoolExecutor getPoolExecutor() {
        return ThreadPoolFactory.singleton().getOrCreateIfAbsent(this);
    }

    /**
     * 判断任务是否在执行中或队列中
     *
     * @param r
     * @return true: 存在
     */
    public boolean contains(Runnable r) {
        return this.getPoolExecutor().contains(r);
    }

    /**
     * 获取执行中的任务
     *
     * @return
     */
    public Set<Map.Entry<Runnable, CustomThread>> getRunningTasks() {
        return this.getPoolExecutor().getRunningTaskThreads();
    }

    /**
     * 获取执行中的任务信息
     *
     * @return
     */
    public List<String> getRunningTaskInfo() {
        List<String> runningTaskInfo = new ArrayList<>();
        Set<Map.Entry<Runnable, CustomThread>> runningTasks = this.getPoolExecutor().getRunningTaskThreads();
        if (runningTasks == null) {
            return runningTaskInfo;
        }
        for (Map.Entry<Runnable, CustomThread> runningTask : runningTasks) {
            Thread thread = runningTask.getValue().getThread();
            runningTaskInfo.add(String.format("%s-%d-%s-%d::%s", thread.getName(), thread.getId(), thread.getState(), thread.getPriority(), runningTask.getKey().toString()));
        }
        return runningTaskInfo;
    }

    /**
     * 获取线程池信息
     *
     * @return
     */
    public Map<String, Object> getPoolInfo() {
        return ThreadPoolFactory.singleton().getPoolInfo(this);
    }

    /**
     * 核心线程的空闲数量
     *
     * @return 空闲数量
     */
    public int getCoreThreadIdleCount() {

        // 核心线程数
        int corePoolSize = this.getPoolExecutor().getCorePoolSize();

        // 执行的任务数量：包括执行中和待执行的
        int executeCount = this.getPoolExecutor().getExecuteAndWait().getTotalCount();

        // 空闲的数量
        int idleCount = corePoolSize - executeCount;

        // 获取任务数量
        return Math.max(idleCount, 0);
    }

    /**
     * 获取线程池信息
     *
     * @return 线程池信息
     */
    public static Map<TaskEnum, Map<String, Object>> getAllPoolInfo() {
        Map<TaskEnum, Map<String, Object>> mapInfo = new HashMap<>();
        TaskEnum[] enumConstants = TaskEnum.class.getEnumConstants();
        for (TaskEnum enumConstant : enumConstants) {
            Map<String, Object> poolInfo = ThreadPoolFactory.singleton().getPoolInfo(enumConstant);
            if (poolInfo != null) {
                mapInfo.put(enumConstant, poolInfo);
            }
        }

        return mapInfo;
    }

    /**
     * 线程池信息格式化为字符串显示
     *
     * @return 线程池信息
     */
    public static StringBuilder printAllPoolInfo() {
        StringBuilder builder = new StringBuilder();
        Map<TaskEnum, Map<String, Object>> threadPoolTaskEnumMapMap = getAllPoolInfo();
        for (Map.Entry<TaskEnum, Map<String, Object>> threadPoolTaskEnumMapEntry : threadPoolTaskEnumMapMap.entrySet()) {
            builder.append("线程池").append(threadPoolTaskEnumMapEntry.getKey()).append("信息：").append('\n');
            for (Map.Entry<String, Object> stringObjectEntry : threadPoolTaskEnumMapEntry.getValue().entrySet()) {
                builder.append(stringObjectEntry.getKey()).append(":").append(stringObjectEntry.getValue()).append('\n');
            }
        }
        return builder;
    }

}

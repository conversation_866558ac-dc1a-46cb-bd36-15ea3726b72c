package com.differ.wdgj.api.user.biz.infrastructure.data.enums;

import com.alibaba.fastjson.annotation.JSONType;

import com.differ.wdgj.api.component.util.enums.*;
import com.differ.wdgj.api.user.biz.infrastructure.exception.AppException;

/**
 * 配置键枚举
 *
 * <AUTHOR>
 * @since 2019-12-23  15:30
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class, serializer = EnumCodeValueWriteSerializer.class)
public enum ConfigKeyEnum implements NameEnum {
    // region 枚举

    // region 系统基础

    MULTI_QUEUE_RETRY_MAX("多队列最大重试次数","|default=10|"),

    MULTI_QUEUE_PULL_FREQUENCY("多队列拉取队列的最小执行频率（单位：秒）", "60"),

    MULTI_QUEUE_PULL_HAND_TRIGGER("手动触发非主动消费的队列，格式：|队列CODE|队列CODE|","||"),

    MULTI_QUEUE_RETRY_ALARM_COUNT("多队列失败重试次数报警阈值", "|api.message.businessretry=5|api.message.loadretry=5|api.message.orderretry=5|"),

    MULTI_QUEUE_CONSUME_TIMEOUT("多队列消费超时报警阈值（毫秒）", "|default=0|"),

    MULTI_QUEUE_BODY_MONITOR_LENGTH("多队列大报文监控的长度", "|default=1024|"),

    /**
     * 要记录监控耗时，任务总耗时最小的耗时时间
     */
    EXEC_MONITOR_LOCAL_LOG_TIME_MIN("要记录监控耗时，任务总耗时最小的耗时时间，默认：1", "1"),

    /**
     * 执行监控的最大超时时间(秒)
     */
    EXEC_MONITOR_MAX_TIMEOUT("执行监控的最大超时时间(秒)", "|chain_log=300|demo=3|"),

    /**
     * 执行监控的记录总数量报警值
     */
    EXEC_MONITOR_TOTAL_RECORD_ALARM_VALUE("执行监控的记录总数量报警值", "1000"),

    /**
     * 本地执行超时的实时报警的最大次数,超过后不再报警
     */
    EXEC_MONITOR_TIMEOUT_ALARM_MAX_COUNT("本地执行超时的实时报警的最大次数", "100"),
    
    /**
     * 队列定时任务执行超时时间（单位：秒），示例：|full:stock.large.member=300|
     */
    QUEUE_JOB_TASK_EXEC_TIMEOUT("队列定时任务执行超时时间（单位：秒），示例：|full:stock.large.member=300|", "||"),

    /**
     * 队列定时任务最大重试次数，示例：|stock.large.member=10|
     */
    QUEUE_JOB_TASK_EXEC_MAX_RETRY_TIMES("队列定时任务最大重试次数，示例：|full:stock.large.member=10|", "||"),

    /**
     * 队列定时任务同步单次获取任务数量，示例：|stock.large.member=5|
     */
    QUEUE_JOB_SYNC_ONCE_FETCH_COUNT("队列定时任务同步单次获取任务数量，示例：|full:stock.large.member=5|", "||"),

    /**
     * 队列定时任务拉取执行频率（单位：秒），示例：|stock.large.member=3|
     */
    QUEUE_JOB_FETCH_EXEC_FREQUENCY("队列定时任务拉取执行频率（单位：秒），示例：|full:stock.large.member=3|", "||"),

    /**
     * 队列定时任务开启监控，格式：|任务编码1|任务编码2|
     */
    QUEUE_JOB_ACTION_MONITOR("队列定时任务开启监控，格式：|任务编码1|任务编码2|", ""),

    /**
     * 队列定时任务最小执行耗时（单位：秒），示例：|full:stock.large.member=5000|
     */
    QUEUE_JOB_MIN_EXEC_SPENT("队列定时任务最小执行耗时（单位：秒），示例：|full:stock.large.member=5|", "||"),
    
    /**
     * 队列定时任务初始化执行频率（单位：秒），示例：|stock.large.member=300|
     */
    QUEUE_JOB_INIT_EXEC_FREQUENCY("队列定时任务初始化执行频率（单位：秒），示例：|full:stock.sync.large=300|", "||"),

    /**
     * 队列定时任务排队最大执行数（单位：秒），示例：|g001:job.queue.demo=5|
     */
    QUEUE_JOB_CODE_MAX_EXEC("队列定时任务会员排队最大执行数（单位：秒），示例：|full:job.queue.t.demo=3|default=5|", "||"),

    /**
     * 队列定时任务会员排队最大执行数（单位：秒）
     */
    QUEUE_JOB_CODE_USER_MAX_EXEC("队列定时任务会员排队最大执行数（单位：秒），示例：|full:job.queue.t.demo=3|default=5|", "||"),

    /**
     * 队列定时任务集群排队最大执行数，
     */
    QUEUE_JOB_CLUSTER_MAX_EXEC("队列定时任务集群排队最大执行数，示例：|g001:job.queue.t.demo-会员名=10|default=20|", "||"),

    /**
     * cpu控制相关
     */
    SYSTEM_CPU_MAX_DEFAULT("系统cpu限制的默认上限", "75.0"),

    /**
     * 会员任务使用稳定的匀速消费任务执行器的等待所有任务完成的最大超时时间（秒），0表示不超时，示例：|AutoDownloadOrderNormalJob=120|
     */
    TASK_USER_USE_STEADY_MAX_WAIT_ALL_TIME_OUT("会员任务使用稳定的匀速消费任务执行器的等待所有任务完成的最大超时时间（秒）", "|default=120|"),

    /**
     * 任务使用稳定的匀速消费任务执行器的等待所有任务完成的最大超时时间（秒），0表示不超时，示例：|AutoDownloadOrderNormalJob=600|
     */
    TASK_USE_STEADY_MAX_WAIT_ALL_TIME_OUT("任务使用稳定的匀速消费任务执行器的等待所有任务完成的最大超时时间（秒）", "|default=600|"),

    /**
     * 等待允许的剩余任务数
     */
    TASK_USE_STEADY_REMAIN_TASK_COUNT("等待允许的剩余任务数", "|default=1|"),

    CRYPTOGRAPHY_AES_KEY("ASC加密密钥(长度大于10位)", "E580512826E840278D7E9DC64E26E982"),
    CRYPTOGRAPHY_AES_IV("ASC加密向量IV(长度大于10位)", "FDAEC1F16CA34BE3804EE9FFF9A55243"),
    SMS_WARNINGMOBILES("紧急报警短信手机号(多个之间以,号分隔如：***********,***********)", "***********,***********"),
    MAXTHREADNUMBER_ASYNCDOMAINEVENTHANDLER("异步执行领域委托异步线程池上限(默认值：10)", "10"),
    DOCKER_NUMBER_SERVICE("Docker服务数量(ass-business服务，默认值：1)", "1"),

    //region 线程池
    /**
     * 线程池是否开启debug信息
     */
    TASK_THREAD_TOOL_DEBUG("线程池是否开启debug信息，示例：线程池名1|线程池名2", ""),
    /**
     * 动态设置线程池的最大线程数值
     */
    TASK_MAX_POOL_SIZE("动态设置线程池的最大线程数值，示例：|线程池名=线程池数|", "0"),
    /**
     * 动态设置线程池的核心线程数
     */
    TASK_CORE_POOL_SIZE("动态设置线程池的核心线程数，示例：|线程池名=线程池数|", "0"),/**
     * 动态设置线程池中任务执行时间报警阈值
     */
    THREAD_TASK_TIMEOUT_TIME("动态设置线程池中任务执行时间报警阈值 示例:|api-order-load=32|", ""),
    /**
     * 动态设置线程池阻塞队列报警值
     */
    TASK_QUEUE_ALARM_VALUE("动态设置线程池阻塞队列报警值", "|default=100|"),
    /**
     * 动态设置时间的报警间隔
     */
    ALARM_INTEVAL_TIME("动态设置报警的时间间隔", "|default=600|"),
    /**
     * 动态设置服务的CPU核数
     */
    SERVER_CPU_CORES("动态设置服务的CPU核数", "|default=8|"),

    //endregion

    //region 报警
    /**
     * 是否允许报警
     */
    ALARM_ENABALE("允许报警的报警类别和会员, 格式：1 或 0 ", "1"),
    /**
     * 允许报警的报警类别和会员
     */
    ALARM_ENABALE_TYPE_AND_MEMBER("允许报警的报警类别和会员, 格式：|类型@会员|", "ALL@ALL"),
    /**
     * 不允许报警的报警类别和会员
     */
    ALARM_UNABALE_TYPE_AND_MEMBER("不允许报警的报警类别和会员, 格式：|类型@会员|", ""),
    /**
     * 报警类型对应通知类型
     */
    ALARM_TYPE_TO_NITUFUCATION_TYPE("报警类型对应通知类型，示例：|报警类型=通知类型1,通知类型2|", "|default=0|"),
    /**
     * 报警类型对应企业微信机器人WEBHOOK
     */
    ALARM_TYPE_TO_QYWX_TEXT_WEBHOOK("报警类型对应企业微信机器人WEBHOOK，示例：|报警类型=WEBHOOK|", "|default=22dcbd0f-2370-45ea-8813-efa838564abb|"),
    /**
     * 报警类型对应企业微信机器人手机号
     */
    ALARM_TYPE_TO_QYWX_TEXT_MOBILE("报警类型对应企业微信机器人手机号，示例：|报警类型=手机1,手机号2|", ""),

    //endregion

    // endregion

    //region 逻辑业务

    //region 系统日志

    /**
     * 是否开启java Info Elk日志
     */
    IsAction_JavaElkLog_Info("是否开启java Elk日志 - Info, 格式：日志标题1|日志标题2","ALL"),


    /**
     * 是否开启java Info 调试灰度 Elk日志
     */
    IsAction_JavaElkLog_Member_Info("是否开启java Info 调试灰度 Elk日志, 格式：日志标题1@灰度键|日志标题2@灰度键",""),

    /**
     * 是否排除java Warn Elk日志
     */
    IsExclude_JavaElkLog_Warn("是否排除java Elk日志 - Warn, 格式：日志标题1|日志标题2",""),

    /**
     * 是否排除java Error Elk日志
     */
    IsExclude_JavaElkLog_Error("是否开启java Elk日志 - Error, 格式：日志标题1|日志标题2",""),

    //endregion

    //region 菠萝派
    /**
     * 请求菠萝派接口所需AppKey
     */
    API_PolyAPI_AppKey("请求菠萝派接口所需AppKey","f2c0a820992743f8b293f4b027265df0"),
    /**
     * 请求菠萝派接口所需AppSecret
     */
    API_PolyAPI_AppSecret("请求菠萝派接口所需AppSecret","1e2c62096829450baac80180193249e2"),
    /**
     * 请求菠萝派公共接口版本号
     */
    API_PublicPolyAPI_Version("请求菠萝派公共接口版本号","1.3"),
    /**
     * 菠萝派模拟响应类型
     */
    Poly_SimulateResponseType("菠萝派模拟响应类型(取值：0 无，1 菠萝派直接返回成功，2 菠萝派直接返回失败，3 请求平台沙箱环境，4 请求测试部门接口模拟成功，5 请求测试部门接口模拟失败)",""),
    /**
     * 是否关闭菠萝派重新机制
     */
    ISCLOSE_POLYAPI_Retry("是否关闭菠萝派重新机制(取值：0，1)","0"),

    //region 菠萝派网关
    /**
     * 是否开启菠萝派同步平台特性特定网关
     */
    PolyAPI_Gateway_Aliyun_PlatFeaturesAction("菠萝派同步平台特性特定网关(公网正式),多个用|分割",""),
    /**
     * 菠萝派同步平台特性特定网关(公网正式),多个用|分割
     */
    PolyAPI_Gateway_Aliyun_PlatFeaturesPublic("菠萝派同步平台特性特定网关(公网正式),多个用|分割",""),
    /**
     * [消息订阅]菠萝派店铺订阅网关(阿里云公网正式)
     */
    Gateway_PolyAPIShopSubscribe_Aliyun_Public("[消息订阅]菠萝派店铺订阅网关(阿里云公网正式)",""),
    /**
     * 菠萝派网关(内网测试)
     */
    Gateway_PolyAPI_LocalTest("菠萝派网关(内网测试)",""),
    /**
     * 菠萝派网关(阿里云公网正式)
     */
    Gateway_PolyAPI_Aliyun_Public("菠萝派网关(阿里云公网正式) ",""),
    /**
     * 京东菠萝派网关(京东内网测试)
     */
    Gateway_PolyAPI_JD_LocalTest("京东菠萝派网关(京东内网测试) ",""),
    /**
     * 京东菠萝派网关(京东公网正式)
     */
    Gateway_PolyAPI_JD_Public("京东菠萝派网关(京东公网正式) ",""),
    /**
     * 请求菠萝派拆分服务配置V2,多个按|分割(平台值1#平台值2^*^接口1#接口2$*$阿里地址1#阿里地址2@京东地址1#京东地址2)
     */
    PolyAPISplitServices_CfgValueV2("请求菠萝派拆分服务配置V2,多个按|分割(平台值1#平台值2^*^接口1#接口2$*$阿里地址1#阿里地址2@京东地址1#京东地址2)",""),
    //endregion

    //region 云平台-唯品会MP
    /**
     * 唯品会MP是否开启云平台
     */
    IsAction_YunBusinessPlat_WPHMP("唯品会MP是否开启云平台 ",""),
    /**
     * 唯品会MP下载订单-内网测试环境网关
     */
    WPHMPGateway_YunBusinessGetOrder_LocalTest("唯品会MP下载订单-内网测试环境网关",""),
    /**
     * 唯品会MP其他-内网测试环境网关
     */
    WPHMPGateway_YunBusiness_LocalTest("唯品会MP其他-内网测试环境网关",""),
    /**
     * 唯品会MP下载订单-线上环境网关
     */
    WPHMPGateway_YunBusinessGetOrder_Public("唯品会MP下载订单-线上环境网关",""),
    /**
     * 唯品会MP其他-线上环境网关
     */
    WPHMPGateway_YunBusiness_Public("唯品会MP其他-线上环境网关 ",""),
    //endregion

    //region 云平台-拼多多
    /**
     * 拼多多是否开启云平台
     */
    IsAction_YunBusinessPlat_PDD("拼多多是否开启云平台 ",""),
    /**
     * 拼多多下载订单-内网测试环境网关
     */
    PddGateway_YunBusinessGetOrder_LocalTest("拼多多下载订单-内网测试环境网关",""),
    /**
     * 拼多多其他-内网测试环境网关
     */
    PddGateway_YunBusiness_LocalTest("拼多多其他-内网测试环境网关",""),
    /**
     * 拼多多下载订单-线上环境网关
     */
    PddGateway_YunBusinessGetOrder_Public("拼多多下载订单-线上环境网关",""),
    /**
     * 拼多多其他-线上环境网关
     */
    PddGateway_YunBusiness_Public("拼多多其他-线上环境网关 ",""),

    /**
     * 是否开启 保存拼多多原始单、售后单时清空CustomerID字段
     */
    IsAction_SaveOrderCustomerIdBlank_UserAndShopId("保存拼多多原始单、售后单时清空CustomerID字段(格式:会员1@外部店铺ID1#外部店铺ID2) ",""),
    //endregion

    //region 云平台-京东
    /**
     * 京东是否开启云平台
     */
    IsAction_YunBusinessPlat("京东是否开启云平台 ",""),
    /**
     * 京东下载订单-内网测试环境网关
     */
    JDGateway_YunBusinessGetOrder_LocalTest("京东下载订单-内网测试环境网关",""),
    /**
     * 京东其他-内网测试环境网关
     */
    JDGateway_YunBusiness_LocalTest("京东其他-内网测试环境网关",""),
    /**
     * 京东下载订单-线上环境网关
     */
    JDGateway_YunBusinessGetOrder_Public("京东下载订单-线上环境网关",""),
    /**
     * 京东其他-线上环境网关
     */
    JDGateway_YunBusiness_Public("京东其他-线上环境网关 ",""),
    //endregion

    //endregion

    //region 库存同步
    /**
     * 是否开启库存同步菠萝派通用错误码处理
     */
    IsAction_SyncStock_PloyCommonErrorCodeProcess("是否开启库存同步菠萝派通用错误码处理, 格式：平台1#平台2@会员1#会员2$错误码1#错误码2",""),

    /**
     * 库存同步菠萝派通用错误码锁定时长
     */
    IsAction_SyncStock_ErrorCodeLimitTime("错误码锁定时长, s格式：错误码1{{配置值}}|错误码2{{配置值}}",""),

    /**
     * 删除无效网点匹配的错误码或者错误消息
     */
    WDGJ_DeleteInvalidStoreMatch("删除无效网点匹配的错误码或者错误消息，格式：关键字A|关键字B","网点不存在"),

    /**
     * 是否启用修改分销商品类型
     */
    IsAction_SyncStockFrequecnyControl_UpdateFenXiaoGoods("是否启用修改分销商品类型（取值：ALL、IP、用户名，如：ALL 或 api2017）",""),

    /**
     * 是否启用上下架失败通知管家功能
     */
    IsAction_SyncStockFrequecnyControl_ShelvesFailedNotify("是否启用上下架失败通知管家功能（取值：ALL、IP、用户名，如：ALL 或 api2017）",""),

    /**
     * 活动商品下次重新触发时间平偏移量（单位秒，默认30）
     */
    NextResetSyncTime_ActivityProduct("活动商品下次重新触发时间平偏移量（单位秒，默认30）","30"),

    /**
     * 关闭库存同步平台值
     */
    ISACTION_CLOSESYNCSTOCKPLAT("关闭库存同步平台值，ALL表示所有平台都关闭(取值:平台值 如:1或1|2 多个值之间以“|”隔开)", ""),

    /**
     * 库存变动通知转换配置
     */
    STOCK_NOTICE_TRANSFORM_CONFIG("库存变动通知转换配置(|ALL=|)", ""),

    /**
     * 库存同步结果回写超时时间
     */
    STOCK_RESULT_TIMEOUT_PERIOD("库存同步结果回写超时时间","2000"),

    /**
     * 普通自动库存同步二级触发频率（单位：秒）
     */
    STOCK_NOTICE_NORMAL_TRIGGER_FREQUENCY("普通自动库存同步二级触发频率（单位：秒）", "20"),
    /**
     * 多仓网店库存同步二级触发频率（单位：秒）
     */
    STOCK_NOTICE_MULTI_TRIGGER_FREQUENCY("多仓网店库存同步二级触发频率（单位：秒）", "20"),
    /**
     * 慢平台网店库存同步二级触发频率（单位：秒）
     */
    STOCK_NOTICE_SLOW_PLAT_TRIGGER_FREQUENCY("慢平台网店库存同步二级触发频率（单位：秒）", "20"),

    /**
     * 普通库存变动通知转换频率（单位：秒）
     */
    STOCK_NOTICE_NORMAL_CONVERT_FREQUENCY("普通库存变动通知转换频率（单位：秒）", "30"),

    /**
     * 多仓库存变动通知转换频率（单位：秒）
     */
    STOCK_NOTICE_MULTI_CONVERT_FREQUENCY("多仓库存变动通知转换频率（单位：秒）", "30"),
    //endregion

    //region 售后下载
    /**
     * 自动下载换货单发起定时任务频率
     */
    LOAD_AFTER_SALE_JOB_MIN_FREQUENCY("自动下载售后单发起定时任务频率",""),

    /**
     * 自动下载换货单发起定时任务频率波动范围
     */
    LOAD_AFTER_SALE_JOB_RANGE("自动下载售后单发起定时任务频率波动范围",""),

    /**
     * 是否关闭自动下载售后单
     */
    CLOSE_AUTO_LOAD_AFTER_SALE_JOB("是否关闭自动下载售后单(格式:平台值1@会员名1#会员名2|平台值2@会员名2#会员名3(不带小黑点))",""),

    /**
     * 开启售后业务java的平台+会员
     */
    ISACTION_AFTERSALE_ENABLEPLATUSER("开启售后业务java的平台+会员(取值：ALL、平台@会员，如平台1@$会员1#会员2)",""),

    /**
     * 开启java售后业务自动下载
     */
    ISACTION_AFTERSALE_AUTO_DOWNLOAD("开启java售后业务自动下载(取值：ALL、会员1|会员2)",""),

    /**
     * 开启 Java下载售后单创建子任务V2版本
     */
    ISACTION_AFTERSALE_CREATESUBTASKV2("Java下载售后单创建子任务V2版本(取值：ALL、会员1|会员2)",""),

    /**
     * 开启 Java下载售后单创建子任务V2版本
     */
    ISACTION_AFTERSALE_DESCPAGERUNMODEV2("Java下载售后单创建子任务V2版本(取值：ALL、会员1|会员2)","ALL"),

    /**
     * 售后单待执行下载任务超时时间（秒） 默认0
     */
    LOAD_AFTERSALE_WORK_TASK_UNRUN_TIMEOUT_PERIOD("售后单待执行下载任务超时时间（秒）",""),

    //endregion

    //region 售后保存
    /**
     * 售后单保存是否关闭hash校验
     */
    ISACTION_AFTERSALE_CLOSEHASHCODECHECK("售后单保存是否关闭hash校验, 格式：平台1#平台2@会员1#会员2",""),
    /**
     * 售后单保存异常关键字是否需要重试
     */
    ISACTION_AFTERSALE_RETRYMESSAGE("售后单保存异常关键字是否需要重试, 格式：关键字1|关键字2|关键字3",""),
    /**
     * 【售后java】需要重新下载的菠萝派返回subcode, 格式：关键字1|关键字2|关键字3
     */
    AFTERSALE_NEEDRETRY_SUBCODE("【售后java】需要重新下载单的菠萝派返回subcode, 格式：关键字1|关键字2|关键字3", "LXO.REQUEST_FAILURE.FAILNEEDRETRY"),

    /**
     * 【售后java】菠萝派订单级返回失败，过滤不保存的subcode或submessage, 格式：关键字1|关键字2|关键字3
     */
    AFTERSALE_RETURNFAIL_SUBCODE_AND_MSG("【售后java】菠萝派订单级返回失败，过滤不保存的subcode或submessage, 格式：关键字1|关键字2|关键字3", ""),
    //endregion

    //region 工作任务
    /**
     * 是否开启 删除X天前过期的工作任务触发数据 定时任务
     */
    ISACTION_DELETE_EXPIRED_DATA("【售后java】是否开启 删除X天前过期的任务（总开关）。//默认为0；1表示开启；", "0"),

    /**
     * 根据taskType配置过期天数，不在配置键中的默认过期天数为15天
     */
    TASK_CONFIG_TASKTYPE_EXPIRED_DAY("【售后java】配置taskType过期天数，不在此配置键中的默认过期天数为15天。格式：|taskType1=过期天数A|taskType2=过期天数B|", ""),

    /**
     * 开启凌晨自动补偿下载售后单的平台 + 会员
     */
    ISACTION_SUPPLEAFTERSALE_ENABLEPLATUSER("开启凌晨自动补偿下载售后单的平台+会员(取值：ALL、平台@会员，如平台A@$会员a#会员b|平台B#平台C@$会员b#会员c)",""),

    /**
     * 是否开启 删除g_api_sysmatch_plat表历史数据 定时任务
     */
    ISACTION_DELETE_SYSMATCH_PLAT_EXPIRED_DATA("是否开启 删除g_api_sysmatch_plat表历史数据（总开关）。//默认为0；1表示开启；", "0"),

    /**
     * g_api_sysmatch_plat表历史数据过期天数配置
     */
    SYSMATCH_PLAT_EXPIRED_DAY("g_api_sysmatch_plat表历史数据过期天数，默认15天", "15"),

    /**
     * 唯品会JIT仓库信息
     */
    GETORDER_VIPJIT_WAREHOUSE("唯品会JIT仓库信息:","<?xml  version=\\\"1.0\\\"  encoding=\\\"utf-8\\\"  ?><!--唯品会JIt仓库配置--><VIPJITWareHouse>  <VIP_NH  WhseCode=\\\"VIP_NH\\\"  value=\\\"1\\\"  Name=\\\"南海仓\\\"  /><VIP_SH  WhseCode=\\\"VIP_SH\\\"  value=\\\"2\\\"  Name=\\\"上海仓\\\"  /><VIP_CD  WhseCode=\\\"VIP_CD\\\"  value=\\\"3\\\"  Name=\\\"成都仓\\\"  /><VIP_BJ  WhseCode=\\\"VIP_BJ\\\"  value=\\\"4\\\"  Name=\\\"北京仓\\\"  /><VIP_HZ  WhseCode=\\\"VIP_HZ\\\"  value=\\\"5\\\"  Name=\\\"鄂州仓\\\"  /><VIP_HH  WhseCode=\\\"VIP_HH\\\"  value=\\\"7\\\"  Name=\\\"花海仓\\\"  /><VIP_ZZ  WhseCode=\\\"VIP_ZZ\\\"  value=\\\"8\\\"  Name=\\\"郑州\\\"  /><VIP_SE  WhseCode=\\\"VIP_SE\\\"  value=\\\"9\\\"  Name=\\\"首尔\\\"  /><VIP_JC  WhseCode=\\\"VIP_JC\\\"  value=\\\"10\\\"  Name=\\\"白云\\\"  /><VIP_DA  WhseCode=\\\"VIP_DA\\\"  value=\\\"11\\\"  Name=\\\"唯品团\\\"  /><VIP_MRC  WhseCode=\\\"VIP_MRC\\\"  value=\\\"12\\\"  Name=\\\"唯品卡\\\"  /><VIP_ZZKG  WhseCode=\\\"VIP_ZZKG\\\"  value=\\\"13\\\"  Name=\\\"郑州空港\\\"  /><VIP_GZNS  WhseCode=\\\"VIP_GZNS\\\"  value=\\\"14\\\"  Name=\\\"广州南沙\\\"  /><VIP_CQKG  WhseCode=\\\"VIP_CQKG\\\"  value=\\\"15\\\"  Name=\\\"重庆空港\\\"  /><VIP_SZGY  WhseCode=\\\"VIP_SZGY\\\"  value=\\\"16\\\"  Name=\\\"苏州工业\\\"  /><VIP_FZPT  WhseCode=\\\"VIP_FZPT\\\"  value=\\\"17\\\"  Name=\\\"福州平潭\\\"  /><VIP_QDHD  WhseCode=\\\"VIP_QDHD\\\"  value=\\\"18\\\"  Name=\\\"青岛黄岛\\\"  /><HT_GZZY  WhseCode=\\\"HT_GZZY\\\"  value=\\\"19\\\"  Name=\\\"广州中远\\\"  /><HT_GZFLXY  WhseCode=\\\"HT_GZFLXY\\\"  value=\\\"20\\\"  Name=\\\"富力心怡仓\\\"  /><VIP_NBJCBS  WhseCode=\\\"VIP_NBJCBS\\\"  value=\\\"21\\\"  Name=\\\"机场保税仓\\\"  /><HT_NBYC  WhseCode=\\\"HT_NBYC\\\"  value=\\\"22\\\"  Name=\\\"云仓代运营\\\"  /><HT_HZHD  WhseCode=\\\"HT_HZHD\\\"  value=\\\"23\\\"  Name=\\\"杭州航都仓\\\"  /><HT_JPRT  WhseCode=\\\"HT_JPRT\\\"  value=\\\"24\\\"  Name=\\\"日本日通仓\\\"  /><HT_AUXNXY  WhseCode=\\\"HT_AUXNXY\\\"  value=\\\"25\\\"  Name=\\\"悉尼心怡仓\\\"  /><HT_USALATM  WhseCode=\\\"HT_USALATM\\\"  value=\\\"26\\\"  Name=\\\"洛杉矶天马仓\\\"  /><HT_USANYTM  WhseCode=\\\"HT_USANYTM\\\"  value=\\\"27\\\"  Name=\\\"纽约天马仓\\\"  /><HT_SZQHBH  WhseCode=\\\"HT_SZQHBH\\\"  value=\\\"28\\\"  Name=\\\"前海保宏仓\\\"  /><FJFZ  WhseCode=\\\"FJFZ\\\"  value=\\\"29\\\"  Name=\\\"福建福州仓\\\"  /><PJ_ZJHZ  WhseCode=\\\"PJ_ZJHZ\\\"  value=\\\"30\\\"  Name=\\\"杭州仓\\\"  /><HNZZ  WhseCode=\\\"HNZZ\\\"  value=\\\"31\\\"  Name=\\\"郑州小仓\\\"  /><SXXA  WhseCode=\\\"SXXA\\\"  value=\\\"32\\\"  Name=\\\"西安小仓\\\"  /><LNSY  WhseCode=\\\"LNSY\\\"  value=\\\"33\\\"  Name=\\\"沈阳小仓\\\"  /><YNKM  WhseCode=\\\"YNKM\\\"  value=\\\"34\\\"  Name=\\\"昆明小仓\\\"  /><GZGY  WhseCode=\\\"GZGY\\\"  value=\\\"35\\\"  Name=\\\"贵阳前置仓\\\"  /><NMGHHHT  WhseCode=\\\"NMGHHHT\\\"  value=\\\"36\\\"  Name=\\\"内蒙古前置仓\\\"  /><SDJN  WhseCode=\\\"SDJN\\\"  value=\\\"37\\\"  Name=\\\"济南前置仓\\\"  /><XJWLMQ  WhseCode=\\\"XJWLMQ\\\"  value=\\\"38\\\"  Name=\\\"新疆前置仓\\\"  /><HLJHEB  WhseCode=\\\"HLJHEB\\\"  value=\\\"39\\\"  Name=\\\"黑龙江哈尔滨前置仓\\\"  /><GXNN  WhseCode=\\\"GXNN\\\"  value=\\\"40\\\"  Name=\\\"广西南宁前置仓\\\"  /><SXTY  WhseCode=\\\"SXTY\\\"  value=\\\"41\\\"  Name=\\\"山西太原前置仓\\\"  /></VIPJITWareHouse>"),

    //endregion

    //endregion

    // endregion
    ;

    // region 构造器

    /**
     * 构造器
     *
     * @param caption      说明
     * @param defaultValue 默认值
     */
    private ConfigKeyEnum(String caption, String defaultValue) {
        this.caption = caption;
        this.defaultValue = defaultValue;
    }

    // endregion

    // region 变量

    /**
     * 标题
     */
    private String caption;

    /**
     * 默认值
     */
    private String defaultValue;

    // endregion

    // region 方法

    /**
     * 获取默认值。
     *
     * @return
     */
    public String getDefaultValue() {
        return this.defaultValue;
    }

    /**
     * 获取标题
     *
     * @return 标题
     */
    public String getCaption() {
        return this.caption;
    }

    /**
     * 获取 枚举值
     *
     * @return 枚举值
     */
    @Override
    public String getName() {
        return this.name();
    }

    /**
     * 枚举值字符串
     *
     * @return S
     */
    @Override
    public String toString() {
        return this.getName();
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static ConfigKeyEnum create(String value) {
        ConfigKeyEnum enumObject = EnumConvertCacheUtil.convert( ConfigKeyEnum.class,value, EnumConvertType.NAME);

        if (null == enumObject) {
            throw new AppException(SystemErrorCodes.LOGICERROR, "未能从字符串“" + value + "”创建枚举对象ConfigKeyEnum");
        }

        return enumObject;
    }


    // endregion
}



package com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.PlatStockNoticeStatusEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncSlowSpeedEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.AutoSyncStockTriggerMessage;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncTriggerCommand;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger.impl.MultiWarehouseAutoStockSyncTrigger;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchPlatDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchPlatMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * MultiWarehouseAutoStockSyncTrigger集成测试
 * 直接操作数据库，验证多仓库存同步业务逻辑的数据库操作结果
 *
 * <AUTHOR>
 * @date 2025/8/11 16:30
 */
//@Ignore
public class MultiWarehouseAutoStockSyncTriggerTest extends AbstractSpringTest {
    //region 变量
    private MultiWarehouseAutoStockSyncTrigger multiWarehouseAutoStockSyncTrigger;

    @Autowired
    private ApiSysMatchPlatMapper apiSysMatchPlatMapper;

    private static final String TEST_MEMBER_NAME = "api2017";
    private List<Long> testDataIds = new ArrayList<>();
    //endregion

    //region 前置执行
    @Before
    public void setUp() {
        // 清理可能存在的测试数据
        cleanupTestData();

        multiWarehouseAutoStockSyncTrigger = new MultiWarehouseAutoStockSyncTrigger();
        apiSysMatchPlatMapper = BeanContextUtil.getBean(ApiSysMatchPlatMapper.class);
    }

    @After
    public void tearDown() {
        // 清理测试数据
        cleanupTestData();
    }
    //endregion

    /**
     * 测试正常流程 - 多仓库存同步
     * 验证触发同步后数据库状态的变化
     */
    @Test
    public void triggerSyncNormalFlowTest() {
        // 准备测试数据（包含平台仓库代码）
        List<ApiSysMatchPlatDO> testData = createMultiWarehouseTestPlatData();
        insertTestPlatData(testData);

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createTestCommands());

        // 执行测试
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
        Assert.assertTrue("应该有数据被更新", updatedData.size() > 0);

        // 验证状态已更新（具体状态值需要根据实际业务逻辑确定）
        for (ApiSysMatchPlatDO platDO : updatedData) {
            Assert.assertNotEquals("状态应该已被更新", PlatStockNoticeStatusEnum.SYNC_SUCCESS, platDO.getStatus());
            // 验证多仓数据特征：平台仓库代码不为空
            Assert.assertTrue("多仓数据应该有平台仓库代码", StringUtils.isNotBlank(platDO.getPlatWarehouseCode()));
        }
    }

    /**
     * 测试空请求处理
     */
    @Test
    public void triggerSyncWithNullRequestTest() {
        try {
            multiWarehouseAutoStockSyncTrigger.triggerSync(null);
            // 如果没有抛出异常，验证数据库没有被意外修改
            List<ApiSysMatchPlatDO> data = queryTestPlatData();
            Assert.assertTrue("空请求不应该影响数据库", data.isEmpty());
        } catch (Exception e) {
            // 预期可能抛出异常，这是正常的
            Assert.assertNotNull("异常信息不应为空", e.getMessage());
        }
    }

    /**
     * 测试空命令列表处理
     */
    @Test
    public void triggerSyncWithEmptyCommandsTest() {
        // 构造空命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(new ArrayList<>());

        // 执行测试
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态
        List<ApiSysMatchPlatDO> data = queryTestPlatData();
        Assert.assertTrue("空命令不应该产生数据", data.isEmpty());
    }

    /**
     * 测试一对多仓库代码映射逻辑
     * 验证一个apiSysMatchId对应多个平台仓库代码的场景
     */
    @Test
    public void testOneToManyWarehouseMappingTest() {
        // 准备一对多测试数据
        List<ApiSysMatchPlatDO> testData = createOneToManyWarehouseTestData();
        insertTestPlatData(testData);

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createTestCommands());

        // 执行测试
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);

        // 验证一对多映射：同一个apiSysMatchId对应多个仓库代码
        List<ApiSysMatchPlatDO> sameMatchIdData = updatedData.stream()
                .filter(x -> x.getApiSysMatchId().equals(2001))
                .collect(Collectors.toList());

        Assert.assertTrue("应该有多个仓库对应同一个apiSysMatchId", sameMatchIdData.size() >= 2);

        // 验证仓库代码不重复
        Set<String> warehouseCodes = sameMatchIdData.stream()
                .map(ApiSysMatchPlatDO::getPlatWarehouseCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        Assert.assertTrue("仓库代码应该不重复", warehouseCodes.size() >= 2);
    }

    /**
     * 测试单个命令处理
     */
    @Test
    public void triggerSyncWithSingleCommandTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createMultiWarehouseTestPlatData();
        insertTestPlatData(testData);

        // 构造单个命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createSingleTestCommand());

        // 执行测试
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
    }

    /**
     * 测试慢速命令处理
     */
    @Test
    public void triggerSyncWithSlowSpeedCommandTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createMultiWarehouseTestPlatData();
        insertTestPlatData(testData);

        // 构造慢速命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createSlowSpeedTestCommands());

        // 执行测试
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
    }

    //region 辅助方法
    /**
     * 创建测试命令列表
     */
    private List<StockSyncTriggerCommand> createTestCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        // 创建第一个命令 - 淘宝平台
        StockSyncTriggerCommand command1 = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats1 = new HashSet<>();
        plats1.add(PolyPlatEnum.BUSINESS_Taobao);
        command1.setPlats(plats1);
        command1.setTriggerCount(100);
        command1.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command1);

        // 创建第二个命令 - 京东平台
        StockSyncTriggerCommand command2 = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats2 = new HashSet<>();
        plats2.add(PolyPlatEnum.BUSINESS_JD);
        command2.setPlats(plats2);
        command2.setTriggerCount(50);
        command2.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command2);

        return commands;
    }

    /**
     * 创建单个测试命令
     */
    private List<StockSyncTriggerCommand> createSingleTestCommand() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats = new HashSet<>();
        plats.add(PolyPlatEnum.BUSINESS_Taobao);
        command.setPlats(plats);
        command.setTriggerCount(100);
        command.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command);

        return commands;
    }

    /**
     * 创建慢速测试命令列表
     */
    private List<StockSyncTriggerCommand> createSlowSpeedTestCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats = new HashSet<>();
        plats.add(PolyPlatEnum.BUSINESS_Taobao);
        command.setPlats(plats);
        command.setTriggerCount(50);
        command.setSlowSpeed(StockSyncSlowSpeedEnum.SLOW_SPEED_1); // 慢速
        commands.add(command);

        return commands;
    }

    /**
     * 创建多仓测试平台数据
     */
    private List<ApiSysMatchPlatDO> createMultiWarehouseTestPlatData() {
        List<ApiSysMatchPlatDO> testData = new ArrayList<>();

        ApiSysMatchPlatDO platDO1 = new ApiSysMatchPlatDO();
        platDO1.setStatus(1); // 待处理状态
        platDO1.setApiSysMatchId(1001);
        platDO1.setPlat(1);
        platDO1.setShopId(1000);
        platDO1.setPlatWarehouseCode("WH001"); // 多仓关键：平台仓库代码不为空
        platDO1.setChangeSource(0); // 多仓关键：平台仓库代码不为空
        platDO1.setCreateTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        platDO1.setModifiedTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        testData.add(platDO1);

        ApiSysMatchPlatDO platDO2 = new ApiSysMatchPlatDO();
        platDO2.setStatus(1); // 待处理状态
        platDO2.setApiSysMatchId(1002);
        platDO2.setPlat(2);
        platDO2.setShopId(1001);
        platDO2.setPlatWarehouseCode("WH002"); // 多仓关键：平台仓库代码不为空
        platDO2.setChangeSource(0);
        platDO2.setCreateTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        platDO2.setModifiedTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        testData.add(platDO2);

        return testData;
    }

    /**
     * 创建一对多仓库测试数据
     * 一个apiSysMatchId对应多个平台仓库代码
     */
    private List<ApiSysMatchPlatDO> createOneToManyWarehouseTestData() {
        List<ApiSysMatchPlatDO> testData = new ArrayList<>();

        // 第一个仓库
        ApiSysMatchPlatDO platDO1 = new ApiSysMatchPlatDO();
        platDO1.setStatus(1); // 待处理状态
        platDO1.setApiSysMatchId(2001); // 使用不同的ID避免冲突
        platDO1.setPlat(1);
        platDO1.setShopId(2000);
        platDO1.setPlatWarehouseCode("WH_A001"); // 第一个仓库代码
        platDO1.setCreateTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        platDO1.setModifiedTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        testData.add(platDO1);

        // 第二个仓库（同一个apiSysMatchId）
        ApiSysMatchPlatDO platDO2 = new ApiSysMatchPlatDO();
        platDO2.setStatus(1); // 待处理状态
        platDO2.setApiSysMatchId(2001); // 相同的apiSysMatchId
        platDO2.setPlat(1);
        platDO2.setShopId(2000);
        platDO2.setPlatWarehouseCode("WH_A002"); // 第二个仓库代码
        platDO2.setCreateTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        platDO2.setModifiedTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        testData.add(platDO2);

        // 第三个仓库（同一个apiSysMatchId）
        ApiSysMatchPlatDO platDO3 = new ApiSysMatchPlatDO();
        platDO3.setStatus(1); // 待处理状态
        platDO3.setApiSysMatchId(2001); // 相同的apiSysMatchId
        platDO3.setPlat(1);
        platDO3.setShopId(2000);
        platDO3.setPlatWarehouseCode("WH_A003"); // 第三个仓库代码
        platDO3.setCreateTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        platDO3.setModifiedTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        testData.add(platDO3);

        return testData;
    }

    /**
     * 插入测试平台数据
     */
    private void insertTestPlatData(List<ApiSysMatchPlatDO> testData) {
        if (CollectionUtils.isEmpty(testData)) {
            return;
        }

        DBSwitchUtil.doDBWithUser(TEST_MEMBER_NAME, () -> {
            for (ApiSysMatchPlatDO platDO : testData) {
                try {
                    apiSysMatchPlatMapper.insert(platDO);
                    if (platDO.getId() != null) {
                        testDataIds.add(platDO.getId());
                    }
                } catch (Exception e) {
                    System.err.println("插入测试数据失败: " + e.getMessage());
                }
            }
            return null;
        });
    }

    /**
     * 查询测试平台数据
     */
    private List<ApiSysMatchPlatDO> queryTestPlatData() {
        if (testDataIds.isEmpty()) {
            return new ArrayList<>();
        }

        return DBSwitchUtil.doDBWithUser(TEST_MEMBER_NAME, () -> {
            try {
                return apiSysMatchPlatMapper.selectByIds(testDataIds);
            } catch (Exception e) {
                System.err.println("查询测试数据失败: " + e.getMessage());
                return new ArrayList<>();
            }
        });
    }

    /**
     * 清理测试数据
     */
    private void cleanupTestData() {
        if (testDataIds.isEmpty()) {
            return;
        }

        DBSwitchUtil.doDBWithUser(TEST_MEMBER_NAME, () -> {
            try {
                apiSysMatchPlatMapper.deleteByIds(testDataIds);
            } catch (Exception e) {
                System.err.println("清理测试数据失败: " + e.getMessage());
            }
            return null;
        });

        testDataIds.clear();
    }
    //endregion
}
